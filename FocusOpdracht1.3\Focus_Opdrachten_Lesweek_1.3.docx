Focus Opdrachten Lesweek 1.3
Application Lifecycle Management

Naam: [Jouw naam]
Studentnummer: [Jouw studentnummer]
Klas: [Jouw klas]
Datum: [Datum]

═══════════════════════════════════════════════════════════════════

OPDRACHT 1: REQUIREMENTS, NFR'S EN MOSCOW
Gekozen applicatie: Discord

═══════════════════════════════════════════════════════════════════

1. REQUIREMENTS LIJST (minimaal 8, waarvan 2 NFR's)

FUNCTIONELE REQUIREMENTS:

FR1: Tekstberichten versturen en ontvangen
Motivatie: Dit is de kernfunctionaliteit van Discord - gebruikers moeten kunnen communiceren via tekstberichten in real-time binnen servers en directe berichten.

FR2: Spraak- en videogesprekken voeren
Motivatie: Voice en video chat zijn essentiële functies voor gaming communities en teams die samen willen werken of spelen.

FR3: Servers aanmaken en beheren
Motivatie: Gebruikers moeten eigen communities kunnen opzetten met verschillende kanalen voor verschillende onderwerpen.

FR4: Kanalen organiseren (tekst, spraak, categorieën)
Motivatie: Structuur is belangrijk voor grote communities om gesprekken georganiseerd te houden.

FR5: Gebruikers uitnodigen en rechten beheren
Motivatie: Serverbeheerders moeten controle hebben over wie toegang heeft en welke permissies zij hebben.

FR6: Bestanden en media delen
Motivatie: Gebruikers willen afbeeldingen, video's, documenten en andere bestanden kunnen delen tijdens gesprekken.

NON-FUNCTIONELE REQUIREMENTS (NFR's):

NFR1: Lage latency voor spraakcommunicatie (< 100ms)
Motivatie: Voor gaming en real-time communicatie is lage vertraging cruciaal voor een goede gebruikerservaring.

NFR2: 99.9% uptime beschikbaarheid
Motivatie: Discord wordt gebruikt door miljoenen gebruikers wereldwijd die afhankelijk zijn van de service voor dagelijkse communicatie.

═══════════════════════════════════════════════════════════════════

2. MOSCOW PRIORITERING

MUST HAVE:
- FR1: Tekstberichten versturen en ontvangen
- FR2: Spraak- en videogesprekken voeren
- NFR2: 99.9% uptime beschikbaarheid

Motivatie: Deze functies vormen de absolute kern van Discord. Zonder deze functies zou Discord niet kunnen functioneren als communicatieplatform.

SHOULD HAVE:
- FR3: Servers aanmaken en beheren
- FR4: Kanalen organiseren
- NFR1: Lage latency voor spraakcommunicatie

Motivatie: Deze functies zijn zeer belangrijk voor de gebruikerservaring en onderscheiden Discord van eenvoudige chat apps, maar de app zou technisch nog kunnen functioneren zonder deze features.

COULD HAVE:
- FR5: Gebruikers uitnodigen en rechten beheren
- FR6: Bestanden en media delen

Motivatie: Deze functies verbeteren de gebruikerservaring aanzienlijk en maken Discord geschikt voor grotere communities, maar zijn niet essentieel voor basiscommunicatie.

WON'T HAVE (voor deze versie):
- Geavanceerde bot integraties
- Screen sharing in groepsgesprekken
- Geavanceerde moderatie tools

Motivatie: Deze functies zijn nice-to-have maar niet kritiek voor de kernfunctionaliteit van Discord.

═══════════════════════════════════════════════════════════════════

3. GEBRUIKERSPROFIEL

Primair gebruikersprofiel: Gaming Enthusiast (16-25 jaar)

Kenmerken:
- Speelt regelmatig online games met vrienden
- Heeft behoefte aan real-time communicatie tijdens gaming
- Wil communities kunnen opbouwen rond specifieke games
- Technisch vaardig en gewend aan digitale platforms
- Gebruikt Discord dagelijks, vaak meerdere uren per dag
- Waardeert lage latency en hoge kwaliteit audio
- Wil kunnen multitasken (chatten terwijl gamen)

Secundaire gebruikersprofielen:
- Studenten die samenwerken aan projecten
- Online communities en hobbygroepen
- Content creators en hun fanbase

═══════════════════════════════════════════════════════════════════

4. REFLECTIE

Wat vond ik lastig bij het verdelen van de requirements?

Het moeilijkste aspect was het onderscheid maken tussen "Must Have" en "Should Have" requirements. Aanvankelijk wilde ik bijna alle functies als "Must Have" classificeren omdat Discord zo'n rijke feature set heeft. Het was uitdagend om terug te gaan naar de absolute kernfunctionaliteit en te bedenken wat Discord minimaal nodig heeft om zijn primaire doel te bereiken.

Specifieke uitdagingen:
- Server management leek eerst essentieel, maar Discord zou technisch kunnen functioneren als alleen een peer-to-peer chat app
- De prioritering van NFR's was moeilijk omdat beide (latency en uptime) cruciaal lijken voor gebruikerservaring
- Het was lastig om objectief te blijven en niet beïnvloed te worden door mijn eigen intensieve gebruik van Discord

Wat heb ik geleerd van deze oefening?

1. Requirements prioritering is subjectief: Verschillende gebruikersgroepen zouden andere prioriteiten hebben. Een zakelijke gebruiker zou bijvoorbeeld rechten beheer hoger prioriteren.

2. Context is belangrijk: De MoSCoW methode werkt alleen goed als je duidelijk definieert voor welke versie/release en welke gebruikersgroep je prioriteert.

3. NFR's zijn vaak ondergewaardeerd: Non-functionele requirements zoals performance en betrouwbaarheid zijn vaak bepalend voor het succes van een applicatie, maar worden makkelijk over het hoofd gezien.

4. Iteratief proces: Requirements prioritering is geen eenmalige activiteit - prioriteiten kunnen veranderen naarmate je meer leert over gebruikersbehoeften en technische beperkingen.

Deze oefening heeft me bewuster gemaakt van hoe complex product management is en hoe belangrijk het is om bewuste keuzes te maken over wat wel en niet te implementeren.

═══════════════════════════════════════════════════════════════════

[OPDRACHT 2 VOLGT LATER]
